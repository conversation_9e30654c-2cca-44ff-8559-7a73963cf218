{"template": "app", "imports": {"Volo.Abp.LeptonXLiteTheme": {"version": "4.3.2", "isInstalled": true}, "Volo.Abp.Account": {"version": "9.3.2", "isInstalled": true}, "Volo.Abp.OpenIddict": {"version": "9.3.2", "isInstalled": true}, "Volo.Abp.Identity": {"version": "9.3.2", "isInstalled": true}, "Volo.Abp.SettingManagement": {"version": "9.3.2", "isInstalled": true}, "Volo.Abp.PermissionManagement": {"version": "9.3.2", "isInstalled": true}, "Volo.Abp.FeatureManagement": {"version": "9.3.2", "isInstalled": true}}, "folders": {"items": {"src": {}, "test": {}}}, "packages": {"Hugo.AssetManagement.Application": {"path": "src/Hugo.AssetManagement.Application/Hugo.AssetManagement.Application.abppkg", "folder": "src"}, "Hugo.AssetManagement.Application.Tests": {"path": "test/Hugo.AssetManagement.Application.Tests/Hugo.AssetManagement.Application.Tests.abppkg", "folder": "test"}, "Hugo.AssetManagement.Domain.Shared": {"path": "src/Hugo.AssetManagement.Domain.Shared/Hugo.AssetManagement.Domain.Shared.abppkg", "folder": "src"}, "Hugo.AssetManagement.Application.Contracts": {"path": "src/Hugo.AssetManagement.Application.Contracts/Hugo.AssetManagement.Application.Contracts.abppkg", "folder": "src"}, "Hugo.AssetManagement.HttpApi": {"path": "src/Hugo.AssetManagement.HttpApi/Hugo.AssetManagement.HttpApi.abppkg", "folder": "src"}, "Hugo.AssetManagement.HttpApi.Client": {"path": "src/Hugo.AssetManagement.HttpApi.Client/Hugo.AssetManagement.HttpApi.Client.abppkg", "folder": "src"}, "Hugo.AssetManagement.EntityFrameworkCore.Tests": {"path": "test/Hugo.AssetManagement.EntityFrameworkCore.Tests/Hugo.AssetManagement.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "Hugo.AssetManagement.EntityFrameworkCore": {"path": "src/Hugo.AssetManagement.EntityFrameworkCore/Hugo.AssetManagement.EntityFrameworkCore.abppkg", "folder": "src"}, "Hugo.AssetManagement.TestBase": {"path": "test/Hugo.AssetManagement.TestBase/Hugo.AssetManagement.TestBase.abppkg", "folder": "test"}, "Hugo.AssetManagement.Domain.Tests": {"path": "test/Hugo.AssetManagement.Domain.Tests/Hugo.AssetManagement.Domain.Tests.abppkg", "folder": "test"}, "Hugo.AssetManagement.HttpApi.Client.ConsoleTestApp": {"path": "test/Hugo.AssetManagement.HttpApi.Client.ConsoleTestApp/Hugo.AssetManagement.HttpApi.Client.ConsoleTestApp.abppkg", "folder": "test"}, "Hugo.AssetManagement.DbMigrator": {"path": "src/Hugo.AssetManagement.DbMigrator/Hugo.AssetManagement.DbMigrator.abppkg", "folder": "src"}, "Hugo.AssetManagement.Blazor": {"path": "src/Hugo.AssetManagement.Blazor/Hugo.AssetManagement.Blazor.abppkg", "folder": "src"}, "Hugo.AssetManagement.Domain": {"path": "src/Hugo.AssetManagement.Domain/Hugo.AssetManagement.Domain.abppkg", "folder": "src"}}}