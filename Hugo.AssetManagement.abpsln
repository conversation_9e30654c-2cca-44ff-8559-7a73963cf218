{
  "id": "ffe5570a-6e87-4c35-9ded-4665df47e327",
  "template": "app",
  "versions": {
    "AbpFramework": "9.3.2",
    "AbpStudio": "1.2.2",
    "TargetDotnetFramework": "net9.0"
  },
  "modules": {
    "Hugo.AssetManagement": {
      "path": "Hugo.AssetManagement.abpmdl"
    }
  },
  "runProfiles": {
    "Default": {
      "path": "etc/abp-studio/run-profiles/Default.abprun.json"
    }
  },
  "options": {
    "httpRequests": {
      "ignoredUrls": [
      
      ]
    }
  },
  "creatingStudioConfiguration": {
    "template": "app",
    "createdAbpStudioVersion": "1.2.2",
    "tiered": "false",
    "runInstallLibs": "true",
    "useLocalReferences": "false",
    "multiTenancy": "false",
    "includeTests": "true",
    "kubernetesConfiguration": "false",
    "uiFramework": "blazor-server",
    "mobileFramework": "none",
    "distributedEventBus": "none",
    "databaseProvider": "ef",
    "runDbMigrator": "true",
    "databaseManagementSystem": "postgresql",
    "separateTenantSchema": "false",
    "createInitialMigration": "true",
    "theme": "leptonx-lite",
    "themeStyle": "",
    "themeMenuPlacement": "",
    "mobileFramework": "none",
    "publicWebsite": "false",
    "socialLogin": "false",
    "selectedLanguages": ["Chinese (Simplified)", "English", ],
    "defaultLanguage": "Chinese (Simplified)",
    "createCommand": "abp new Hugo.AssetManagement -t app --ui-framework blazor-server --database-provider ef --database-management-system postgresql --theme leptonx-lite --without-cms-kit --sample-crud-page --dont-run-bundling --no-multi-tenancy --no-social-logins -no-gdpr -no-openiddict-admin-ui -no-audit-logging -no-file-management -no-language-management -no-text-template-management"
  }
}