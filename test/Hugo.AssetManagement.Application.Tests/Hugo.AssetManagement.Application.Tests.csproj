<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Hugo.AssetManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Hugo.AssetManagement.Application\Hugo.AssetManagement.Application.csproj" />
    <ProjectReference Include="..\Hugo.AssetManagement.Domain.Tests\Hugo.AssetManagement.Domain.Tests.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
  </ItemGroup>

</Project>
