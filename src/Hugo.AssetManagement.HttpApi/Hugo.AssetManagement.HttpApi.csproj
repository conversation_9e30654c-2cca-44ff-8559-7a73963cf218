<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Hugo.AssetManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hugo.AssetManagement.Application.Contracts\Hugo.AssetManagement.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.3.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Account.HttpApi" Version="9.3.2" />
  </ItemGroup>

</Project>
