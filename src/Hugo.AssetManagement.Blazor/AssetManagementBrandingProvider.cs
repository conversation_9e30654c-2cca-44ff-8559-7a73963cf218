using Microsoft.Extensions.Localization;
using Hugo.AssetManagement.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace Hugo.AssetManagement.Blazor;

[Dependency(ReplaceServices = true)]
public class AssetManagementBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<AssetManagementResource> _localizer;

    public AssetManagementBrandingProvider(IStringLocalizer<AssetManagementResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}
