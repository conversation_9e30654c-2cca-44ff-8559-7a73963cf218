<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Hugo.AssetManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hugo.AssetManagement.Domain.Shared\Hugo.AssetManagement.Domain.Shared.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.3.2" />
  </ItemGroup>

</Project>
