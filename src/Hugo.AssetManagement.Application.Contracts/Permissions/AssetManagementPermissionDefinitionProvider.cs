using Hugo.AssetManagement.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace Hugo.AssetManagement.Permissions;

public class AssetManagementPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(AssetManagementPermissions.GroupName);

        var booksPermission = myGroup.AddPermission(AssetManagementPermissions.Books.Default, L("Permission:Books"));
        booksPermission.AddChild(AssetManagementPermissions.Books.Create, L("Permission:Books.Create"));
        booksPermission.AddChild(AssetManagementPermissions.Books.Edit, L("Permission:Books.Edit"));
        booksPermission.AddChild(AssetManagementPermissions.Books.Delete, L("Permission:Books.Delete"));
        //Define your own permissions here. Example:
        //myGroup.AddPermission(AssetManagementPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<AssetManagementResource>(name);
    }
}
