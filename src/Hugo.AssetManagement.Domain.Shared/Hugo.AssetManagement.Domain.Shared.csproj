<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Hugo.AssetManagement</RootNamespace>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain.Shared" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain.Shared" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain.Shared" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Shared" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain.Shared" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.Domain.Shared" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.GlobalFeatures" Version="9.3.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Domain.Shared" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Identity.Domain.Shared" Version="9.3.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\AssetManagement\*.json" />
    <Content Remove="Localization\AssetManagement\*.json" />
  </ItemGroup>

</Project>
