<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Hugo.AssetManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hugo.AssetManagement.Application.Contracts\Hugo.AssetManagement.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi.Client" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi.Client" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi.Client" Version="9.3.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi.Client" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Account.HttpApi.Client" Version="9.3.2" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="**\*generate-proxy.json" />
    <Content Remove="**\*generate-proxy.json" />
  </ItemGroup>

</Project>
