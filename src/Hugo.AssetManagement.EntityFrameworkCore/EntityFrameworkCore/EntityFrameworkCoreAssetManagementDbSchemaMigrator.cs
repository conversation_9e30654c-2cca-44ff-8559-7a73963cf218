using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Hugo.AssetManagement.Data;
using Volo.Abp.DependencyInjection;

namespace Hugo.AssetManagement.EntityFrameworkCore;

public class EntityFrameworkCoreAssetManagementDbSchemaMigrator
    : IAssetManagementDbSchemaMigrator, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public EntityFrameworkCoreAssetManagementDbSchemaMigrator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        /* We intentionally resolving the AssetManagementDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<AssetManagementDbContext>()
            .Database
            .MigrateAsync();
    }
}
