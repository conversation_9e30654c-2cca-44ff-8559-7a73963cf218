using AutoMapper;
using Hugo.AssetManagement.Books;

namespace Hugo.AssetManagement;

public class AssetManagementApplicationAutoMapperProfile : Profile
{
    public AssetManagementApplicationAutoMapperProfile()
    {
        CreateMap<Book, BookDto>();
        CreateMap<CreateUpdateBookDto, Book>();
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */
    }
}
