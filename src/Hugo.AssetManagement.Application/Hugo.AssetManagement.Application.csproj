<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Hugo.AssetManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hugo.AssetManagement.Domain\Hugo.AssetManagement.Domain.csproj" />
    <ProjectReference Include="..\Hugo.AssetManagement.Application.Contracts\Hugo.AssetManagement.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.3.2" />
  </ItemGroup>

</Project>
