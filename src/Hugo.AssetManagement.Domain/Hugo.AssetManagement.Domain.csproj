<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Hugo.AssetManagement</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hugo.AssetManagement.Domain.Shared\Hugo.AssetManagement.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Emailing" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Caching" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.Domain" Version="9.3.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Domain" Version="9.3.2" />
    <PackageReference Include="Volo.Abp.Identity.Domain" Version="9.3.2" />
  </ItemGroup>

</Project>
